<!DOCTYPE html>
<html>

<head>
    <title>Student Grading Result</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css" media="screen">
        /* Base styles */
        html {
            font-family: "DejaVu Sans", sans-serif; /* Consistent font */
            line-height: 1.15;
            margin: 0;
        }

        body {
            font-weight: 400;
            line-height: 1.4; /* Tighter line height for more content */
            color: #212529;
            text-align: left;
            background-color: #fff;
            font-size: 11px; /* Smaller base font for more content per page */
            margin: 15pt 25pt; /* Tighter margins for more space */
        }

        /* Typography */
        h4, .h4 {
            margin-top: 0;
            margin-bottom: 0.3rem; /* Reduced margin for tighter layout */
            font-weight: 500;
            line-height: 1.1; /* Tighter line height */
            font-size: 1.1rem; /* Smaller heading for more space */
        }

        p {
            margin-top: 0;
            margin-bottom: 0.4rem; /* Reduced paragraph spacing */
            line-height: 1.2; /* Tighter line height */
        }

        strong {
            font-weight: bolder;
        }

        ul {
            padding-left: 20px;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }
        li {
            margin-bottom: 0.3rem;
        }

        /* Images */
        img {
            vertical-align: middle;
            border-style: none;
            max-width: 100%; /* Ensure images don't overflow */
            height: auto;
        }

        /* Tables */
        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            text-align: inherit;
            padding: 0.5rem; /* Standardized padding */
            vertical-align: top;
            border: 1px solid #dee2e6; /* Default border for all cells */
            line-height: 1.3;
        }

        thead th {
            font-weight: bold;
            background-color: #e9ecef; /* Lighter header background */
            border-bottom-width: 2px;
            text-align: center;
            vertical-align: middle;
        }

        /* Specific table adjustments */
        .table-header td { /* Table for logo/header */
            border: none; /* Remove borders in the header table */
            padding: 0;
            vertical-align: middle;
        }

        .table-header {
            margin-bottom: 0.5rem; /* Reduced margin for tighter layout */
        }

        .tg { /* Main results table */
            margin-top: 0.8rem; /* Reduced margin for tighter layout */
            margin-bottom: 0.5rem; /* Reduced margin for tighter layout */
            font-size: 10px; /* Smaller font for more rows per page */
            width: 100%; /* Ensure full width */
            border-collapse: collapse; /* Ensure proper border collapse */
        }

        .tg th {
            background-color: #3498db; /* IMSAA Primary blue */
            color: #ffffff; /* White text for header */
            font-weight: bold;
            text-align: center;
            padding: 4px 6px; /* Optimized padding for readability */
            font-size: 10px; /* Consistent font size */
            letter-spacing: 0.3px; /* Professional spacing */
        }

        .tg td {
            padding: 3px 4px; /* Reduced padding for tighter rows */
            line-height: 1.1; /* Tighter line height for more rows */
        }

        .tg .tg-cly1, .tg .tg-zr06 {
            text-align: left;
            vertical-align: middle;
        }

        /* Utilities */
        .mt-5 {
            margin-top: 2rem !important; /* Adjust spacing */
        }
        .mb-0 { margin-bottom: 0 !important; }
        .pl-0 { padding-left: 0 !important; }
        .text-right { text-align: right !important; }
        .text-center { text-align: center !important; }
        .text-uppercase { text-transform: uppercase !important; }
        .border-0 { border: none !important; }
        .cool-gray { color: #3498db; } /* IMSAA Primary blue */
        .red { color: #e74c3c; } /* IMSAA Red accents */
        .dark-blue { color: #2980b9; } /* IMSAA Darker blue */
        .professional-gray { color: #2c3e50; } /* Professional gray */
        .secondary-gray { color: #34495e; } /* Secondary gray */

        /* Header specific styles - Modern Professional Design */
        .header-logo {
            text-align: left;
            vertical-align: middle;
        }
        .header-info {
            text-align: center;
            vertical-align: middle;
        }
        .header-info h4 {
            font-size: 0.85rem; /* Compact professional heading */
            margin-bottom: 0.05rem; /* Minimal margin */
            font-weight: 600; /* Slightly bolder for hierarchy */
            letter-spacing: 0.5px; /* Professional spacing */
        }
        .header-info p {
            margin-bottom: 0.05rem; /* Minimal margin */
            font-size: 7.5px; /* Optimized for space efficiency */
            line-height: 1.0; /* Ultra-tight line height */
            font-weight: 400;
        }

        /* Title section optimization */
        .title-section {
            margin: 0.8rem 0 0.6rem 0; /* Reduced margins */
            padding: 0.4rem 0; /* Minimal padding */
        }
        .title-section .main-title {
            font-size: 1.0rem; /* Compact title */
            font-weight: 600;
            color: #2c3e50; /* Professional dark gray */
            margin-bottom: 0.4rem;
            letter-spacing: 0.3px;
        }
        .title-section .info-grid {
            display: inline-block;
            width: 100%;
            font-size: 8.5px; /* Compact info text */
            line-height: 1.2;
        }
        .title-section .info-row {
            margin-bottom: 0.2rem;
        }
        .title-section .info-label {
            font-weight: 600;
            color: #34495e; /* Professional gray */
            display: inline-block;
            min-width: 60px;
        }
        .title-section .info-list {
            display: inline;
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .title-section .info-list li {
            display: inline;
            margin-right: 8px;
            color: #2980b9; /* IMSAA blue */
            font-weight: 500;
        }
        .title-section .info-list li:after {
            content: " •";
            color: #bdc3c7;
            margin-left: 4px;
        }
        .title-section .info-list li:last-child:after {
            content: "";
        }

        /* Footer text */
        .footer-text {
            margin-top: 1rem; /* Reduced margin for tighter layout */
            font-size: 9px; /* Smaller footer text */
            line-height: 1.1; /* Tighter line height */
        }

        /* Print specific styles - A4 Optimized for Professional Academic Documents */
        @media print {
            body {
                margin: 8pt 15pt; /* Optimized margins for maximum content area */
                font-size: 9.5pt; /* Optimized font size for readability and space */
                line-height: 1.2; /* Improved line spacing */
            }

            /* Header optimization for print */
            .table-header {
                margin-bottom: 0.3rem !important;
            }
            .table-header img {
                height: 50px !important; /* Compact logo for print */
            }
            .header-info h4 {
                font-size: 0.75rem !important; /* Compact header text */
            }
            .header-info p {
                font-size: 7px !important; /* Ultra-compact header details */
            }

            /* Title section optimization */
            .title-section {
                margin: 0.5rem 0 0.4rem 0 !important;
                padding: 0.2rem 0 !important;
            }
            .title-section .main-title {
                font-size: 0.9rem !important;
                margin-bottom: 0.3rem !important;
            }
            .title-section .info-grid {
                font-size: 8px !important;
            }

            /* Table optimizations for maximum content */
            .tg {
                margin-top: 0.4rem !important;
                margin-bottom: 0.3rem !important;
                font-size: 9px !important; /* Optimized for more rows per page */
            }
            .tg th {
                padding: 2px 3px !important; /* Compact header padding */
                font-size: 9px !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }
            .tg td {
                padding: 2px 3px !important; /* Compact cell padding */
                line-height: 1.1 !important; /* Tight line height for more rows */
            }

            /* Page break optimizations */
            table {
                page-break-inside: auto;
            }
            thead {
                display: table-header-group; /* Header repeats on each page */
            }
            tr {
                page-break-inside: avoid;
            }
            td, th {
                page-break-inside: avoid;
            }

            /* Footer optimization */
            .footer-text {
                margin-top: 0.4rem !important;
                font-size: 8px !important;
            }

            /* Hide non-essential elements */
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>

<body>

    <table class="table-header"> <!-- Use specific class for header table -->
        <tbody>
            <tr>
                <td class="header-logo" width="18%"> <!-- Optimized width for space efficiency -->
                    <img src="https://www.institut-imsaa.com/image/logo/logo2-removebg-preview.png" alt="logo IMSAA"
                        height="55"> <!-- Optimized height for professional compact layout -->
                </td>
                <td class="header-info"> <!-- Use specific class -->
                    <h4 class="text-uppercase cool-gray mb-0"> <!-- Removed strong, adjusted margin -->
                        <span class="red">I</span>NSTITUT DE <span class="red">M</span>ANAGEMENT ET
                            DES <span class="red">S</span>CIENCES <span class="red">A</span>PPLIQUEES <br>D'<span
                                class="red">A</span>NTSIRANANA
                    </h4>
                    <p class="mb-0">DIRECTION GENERALE</p>
                    <p class="mb-0">DIRECTION DES ETUDES DE LA RECHERCHE ET DE L’ASSURANCE QUALITE</p>
                    <p class="mb-0">Domaine : <?php echo e($parcours->first()->mention->domaine->nom); ?></p>
                </td>
            </tr>
        </tbody>
    </table>

    <!-- Modern Professional Title Section -->
    <div class="title-section text-center">
        <?php if(!empty($notes)): ?>
            <div class="main-title">Liste des résultats en <?php echo e($niveau->nom); ?> par ordre de mérite</div>

            <div class="info-grid">
                <div class="info-row">
                    <span class="info-label">Semestres :</span>
                    <ul class="info-list">
                        <?php $__currentLoopData = $semestres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $semestre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($semestre->nom); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

                <div class="info-row">
                    <span class="info-label">Parcours :</span>
                    <ul class="info-list">
                        <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($parcour->nom); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

                <div class="info-row">
                    <span class="info-label">Année :</span>
                    <span style="color: #2980b9; font-weight: 500;"><?php echo e($annee->nom); ?></span>
                </div>
            </div>
        <?php else: ?>
            <div class="main-title" style="color: #e74c3c;">Pas de résultat disponible</div>
        <?php endif; ?>
    </div>

    <table class="tg">
        <thead>
            <tr>
                <th class="tg-cly1" width="8%">RANG</th>
                <th class="tg-cly1" width="50%">NOM ET PRENOMS</th>
                <th class="tg-cly1" width="12%">MOYENNE</th>
                <th class="tg-cly1" width="20%">MENTION</th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr> <!-- Optimized row with average column -->
                    <td class="tg-zr06 text-center"><?php echo e($result["rang"] ?? $loop->iteration); ?></td>
                    <td class="tg-zr06">
                        <?php echo e($result["nom"]); ?> <?php echo e($result["prenom"]); ?>

                    </td>
                    <td class="tg-zr06 text-center">
                        <strong style="color: #2980b9;"><?php echo e($result["moy"]); ?></strong>
                    </td>
                    <td class="tg-zr06 text-center">
                        <?php echo e($result["mention"]); ?>

                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="4" class="text-center"> <!-- Updated colspan -->
                        <p class="mb-0">
                            Impossible de générer. Pas encore de note ajoutée !
                        </p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <div class="footer-text"> <!-- Combine footer text for tighter layout -->
        <p style="margin-bottom: 0.2rem;">Arrêté la présente liste au nombre de <?php echo e(count($notes)); ?> étudiant(s).</p>
        <p style="margin-bottom: 0.5rem;">Fait à Antsiranana, le <?php echo e(\Carbon\Carbon::now()->locale('fr')->isoFormat('DD MMMM YYYY')); ?></p>
    </div>
    <!-- Optimized signature section -->
    <table style="width: 100%; margin-top: 1rem; border: none;">
        <tr style="border: none;">
            <td style="width: 50%; text-align: center; border: none; font-size: 9px;"></td>
            <td style="width: 50%; text-align: center; border: none; font-size: 9px;">La Direction Générale</td>
        </tr>
    </table>

</body>

</html>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/pdf/resultat.blade.php ENDPATH**/ ?>