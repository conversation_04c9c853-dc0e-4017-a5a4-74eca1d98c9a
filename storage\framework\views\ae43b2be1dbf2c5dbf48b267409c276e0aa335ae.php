<!DOCTYPE html>
<html>

<head>
    <title>Student Grading Result</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css">
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-family: "Arial", "DejaVu Sans", sans-serif;
            line-height: 1.3;
        }

        body {
            font-weight: 400;
            line-height: 1.3;
            color: #2c3e50;
            background-color: #ffffff;
            font-size: 11px;
            margin: 0;
            padding: 15mm 20mm;
        }

        /* Typography */
        h1, h2, h3, h4 {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        h1 { font-size: 16px; }
        h2 { font-size: 14px; }
        h3 { font-size: 13px; }
        h4 { font-size: 12px; }

        p {
            margin-bottom: 4px;
            line-height: 1.3;
            font-size: 11px;
        }

        strong {
            font-weight: bold;
        }

        /* Header Section */
        .document-header {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 15px;
            width: 100%;
        }

        .header-table {
            width: 100%;
            border-collapse: collapse;
        }

        .header-table td {
            border: none;
            padding: 0;
            vertical-align: middle;
        }

        .header-logo {
            width: 80px;
            text-align: left;
        }

        .header-logo img {
            height: 60px;
            width: auto;
            max-width: 100%;
        }

        .header-content {
            text-align: center;
            padding-left: 20px;
        }

        .institution-name {
            font-size: 14px;
            font-weight: bold;
            line-height: 1.2;
            margin-bottom: 3px;
            text-transform: uppercase;
            color: #2c3e50;
        }

        .red-accent {
            color: #e74c3c;
        }

        .institution-subtitle {
            font-size: 10px;
            line-height: 1.2;
            margin-bottom: 2px;
            color: #34495e;
        }

        /* Title Section */
        .title-section {
            text-align: center;
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #3498db;
            background-color: #f8f9fa;
        }

        .main-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            text-transform: uppercase;
            color: #2c3e50;
        }

        .subtitle-info {
            font-size: 10px;
            margin-bottom: 4px;
            font-weight: bold;
            color: #34495e;
        }

        .info-list {
            margin: 3px 0;
            padding: 0;
            list-style: none;
        }

        .info-list li {
            display: inline-block;
            margin-right: 10px;
            font-size: 10px;
            border: 1px solid #3498db;
            padding: 2px 6px;
            background-color: #ffffff;
        }

        /* Results Table */
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 10px;
            border: 2px solid #3498db;
        }

        .results-table th {
            background-color: #3498db;
            color: #ffffff;
            padding: 8px 6px;
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            text-transform: uppercase;
            border: 1px solid #2980b9;
        }

        .results-table td {
            padding: 6px;
            vertical-align: middle;
            border: 1px solid #bdc3c7;
            line-height: 1.2;
        }

        .results-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        /* Column Widths */
        .col-rank {
            width: 8%;
            text-align: center;
            font-weight: bold;
            color: #2980b9;
        }

        .col-name {
            width: 67%;
            text-align: left;
        }

        .col-mention {
            width: 25%;
            text-align: center;
            font-weight: bold;
        }

        /* Footer Section */
        .footer-section {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #3498db;
        }

        .footer-text {
            font-size: 10px;
            margin-bottom: 3px;
            line-height: 1.3;
            color: #34495e;
        }

        .signature-section {
            margin-top: 15px;
            width: 100%;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
        }

        .signature-table td {
            border: none;
            padding: 0;
            text-align: center;
            vertical-align: top;
            width: 50%;
        }

        .signature-title {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .text-uppercase { text-transform: uppercase; }
        .mb-0 { margin-bottom: 0; }
        .fw-bold { font-weight: bold; }

        /* Print Optimization */
        @media print {
            @page {
                size: A4;
                margin: 15mm 20mm;
            }

            body {
                margin: 0;
                padding: 0;
                font-size: 10px;
                line-height: 1.2;
                color: #000000;
                background: white;
            }

            /* Ensure blue header prints */
            .results-table th {
                background-color: #3498db !important;
                color: #ffffff !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .results-table tbody tr:nth-child(even) {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .document-header {
                border-bottom-color: #3498db !important;
            }

            .title-section {
                border-color: #3498db !important;
                background-color: #f8f9fa !important;
            }

            .footer-section {
                border-top-color: #3498db !important;
            }

            /* Page break optimization */
            .document-header {
                page-break-after: avoid;
            }

            .title-section {
                page-break-after: avoid;
            }

            .results-table {
                page-break-inside: auto;
            }

            .results-table thead {
                display: table-header-group;
            }

            .results-table tbody tr {
                page-break-inside: avoid;
            }

            .footer-section {
                page-break-inside: avoid;
            }

            /* Optimize spacing for print */
            .title-section {
                margin-bottom: 12px;
                padding: 8px;
            }

            .results-table th {
                padding: 6px 4px;
            }

            .results-table td {
                padding: 4px;
            }

            .footer-section {
                margin-top: 15px;
            }

            /* Remove any shadows or effects */
            * {
                box-shadow: none !important;
                text-shadow: none !important;
            }

            /* Ensure proper borders */
            .document-header,
            .title-section,
            .results-table,
            .results-table th,
            .results-table td,
            .footer-section {
                border-color: #000000 !important;
            }

            /* Tighter spacing for more content */
            .header-logo img {
                height: 50px;
            }

            .institution-name {
                font-size: 13px;
            }

            .main-title {
                font-size: 13px;
            }
        }

        /* Screen preview */
        @media screen {
            body {
                max-width: 210mm;
                margin: 20px auto;
                border: 1px solid #cccccc;
                padding: 15mm 20mm;
            }
        }
    </style>
</head>

<body>
    <!-- Document Header -->
    <div class="document-header">
        <table class="header-table">
            <tr>
                <td class="header-logo">
                    <img src="https://www.institut-imsaa.com/image/logo/logo2-removebg-preview.png" alt="logo IMSAA">
                </td>
                <td class="header-content">
                    <div class="institution-name">
                        <span class="red-accent">I</span>NSTITUT DE <span class="red-accent">M</span>ANAGEMENT ET DES <span class="red-accent">S</span>CIENCES <span class="red-accent">A</span>PPLIQUÉES<br>
                        D'<span class="red-accent">A</span>NTSIRANANA
                    </div>
                    <p class="institution-subtitle">DIRECTION GÉNÉRALE</p>
                    <p class="institution-subtitle">DIRECTION DES ÉTUDES DE LA RECHERCHE ET DE L'ASSURANCE QUALITÉ</p>
                    <p class="institution-subtitle">Domaine : <?php echo e($parcours->first()->mention->domaine->nom); ?></p>
                </td>
            </tr>
        </table>
    </div>

    <!-- Title Section -->
    <div class="title-section">
        <?php if(!empty($notes)): ?>
            <div class="main-title">Liste des résultats en <?php echo e($niveau->nom); ?> par ordre de mérite</div>
            
            <div class="subtitle-info">Semestres :</div>
            <ul class="info-list">
                <?php $__currentLoopData = $semestres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $semestre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($semestre->nom); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            
            <div class="subtitle-info">Parcours :</div>
            <ul class="info-list">
                <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($parcour->nom); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            
            <div class="subtitle-info">Année universitaire : <?php echo e($annee->nom); ?></div>
        <?php else: ?>
            <div class="main-title">Aucun résultat disponible</div>
        <?php endif; ?>
    </div>

    <!-- Results Table -->
    <table class="results-table">
        <thead>
            <tr>
                <th class="col-rank">RANG</th>
                <th class="col-name">NOM ET PRÉNOMS</th>
                <th class="col-mention">MENTION</th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td class="col-rank"><?php echo e($loop->iteration); ?></td>
                    <td class="col-name"><?php echo e($result["nom"]); ?> <?php echo e($result["prenom"]); ?></td>
                    <td class="col-mention"><?php echo e($result["mention"]); ?></td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="3" class="text-center">
                        <p class="mb-0">Impossible de générer. Pas encore de note ajoutée !</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- Footer Section -->
    <div class="footer-section">
        <p class="footer-text">Arrêté la présente liste au nombre de <?php echo e(count($notes)); ?> étudiant(s).</p>
        <p class="footer-text">Fait à Antsiranana, le <?php echo e(\Carbon\Carbon::now()->locale('fr')->isoFormat('DD MMMM YYYY')); ?></p>
        
        <div class="signature-section">
            <table class="signature-table">
                <tr>
                    <td>
                        <div class="signature-title"></div>
                    </td>
                    <td>
                        <div class="signature-title">La Direction Générale</div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>

</html><?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/pdf/resultat.blade.php ENDPATH**/ ?>