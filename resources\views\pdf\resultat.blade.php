<!DOCTYPE html>
<html>

<head>
    <title>Student Grading Result</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css" media="screen">
        /* Base styles */
        html {
            font-family: "DejaVu Sans", sans-serif; /* Consistent font */
            line-height: 1.15;
            margin: 0;
        }

        body {
            font-weight: 400;
            line-height: 1.4; /* Tighter line height for more content */
            color: #212529;
            text-align: left;
            background-color: #fff;
            font-size: 11px; /* Smaller base font for more content per page */
            margin: 15pt 25pt; /* Tighter margins for more space */
        }

        /* Typography */
        h4, .h4 {
            margin-top: 0;
            margin-bottom: 0.3rem; /* Reduced margin for tighter layout */
            font-weight: 500;
            line-height: 1.1; /* Tighter line height */
            font-size: 1.1rem; /* Smaller heading for more space */
        }

        p {
            margin-top: 0;
            margin-bottom: 0.4rem; /* Reduced paragraph spacing */
            line-height: 1.2; /* Tighter line height */
        }

        strong {
            font-weight: bolder;
        }

        ul {
            padding-left: 20px;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }
        li {
            margin-bottom: 0.3rem;
        }

        /* Images */
        img {
            vertical-align: middle;
            border-style: none;
            max-width: 100%; /* Ensure images don't overflow */
            height: auto;
        }

        /* Tables */
        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            text-align: inherit;
            padding: 0.5rem; /* Standardized padding */
            vertical-align: top;
            border: 1px solid #dee2e6; /* Default border for all cells */
            line-height: 1.3;
        }

        thead th {
            font-weight: bold;
            background-color: #e9ecef; /* Lighter header background */
            border-bottom-width: 2px;
            text-align: center;
            vertical-align: middle;
        }

        /* Specific table adjustments */
        .table-header td { /* Table for logo/header */
            border: none; /* Remove borders in the header table */
            padding: 0;
            vertical-align: middle;
        }

        .table-header {
            margin-bottom: 0.5rem; /* Reduced margin for tighter layout */
        }

        .tg { /* Main results table */
            margin-top: 0.8rem; /* Reduced margin for tighter layout */
            margin-bottom: 0.5rem; /* Reduced margin for tighter layout */
            font-size: 10px; /* Smaller font for more rows per page */
            width: 100%; /* Ensure full width */
            border-collapse: collapse; /* Ensure proper border collapse */
        }

        .tg th {
            background-color: #3B71CA; /* Restore original header color */
            color: #ffffff; /* White text for header */
            font-weight: bold;
            text-align: center;
            padding: 3px 4px; /* Reduced padding for tighter rows */
            font-size: 10px; /* Ensure consistent font size */
        }

        .tg td {
            padding: 3px 4px; /* Reduced padding for tighter rows */
            line-height: 1.1; /* Tighter line height for more rows */
        }

        .tg .tg-cly1, .tg .tg-zr06 {
            text-align: left;
            vertical-align: middle;
        }

        /* Utilities */
        .mt-5 {
            margin-top: 2rem !important; /* Adjust spacing */
        }
        .mb-0 { margin-bottom: 0 !important; }
        .pl-0 { padding-left: 0 !important; }
        .text-right { text-align: right !important; }
        .text-center { text-align: center !important; }
        .text-uppercase { text-transform: uppercase !important; }
        .border-0 { border: none !important; }
        .cool-gray { color: #3B71CA; }
        .red { color: #DC4C64; }

        /* Header specific styles */
        .header-logo {
            text-align: left;
        }
        .header-info {
            text-align: center;
        }
        .header-info h4 {
            font-size: 0.9rem; /* Even smaller heading in header */
            margin-bottom: 0.1rem; /* Reduced margin */
        }
        .header-info p {
            margin-bottom: 0.1rem; /* Reduced margin */
            font-size: 8px; /* Smaller text in header */
            line-height: 1.1; /* Tighter line height */
        }

        /* Footer text */
        .footer-text {
            margin-top: 1rem; /* Reduced margin for tighter layout */
            font-size: 9px; /* Smaller footer text */
            line-height: 1.1; /* Tighter line height */
        }

        /* Print specific styles (enhanced for optimal page usage) */
        @media print {
            body {
                margin: 10pt 20pt; /* Even tighter margins for print */
                font-size: 10pt; /* Smaller font size for more content per page */
            }

            /* Table optimizations */
            table {
                page-break-inside: auto; /* Allow page breaks within tables */
            }

            thead {
                display: table-header-group; /* Ensure header repeats on each page */
            }

            tr {
                page-break-inside: avoid; /* Try to keep rows together */
            }

            td, th {
                page-break-inside: avoid; /* Avoid breaking cells */
            }

            /* Hide non-essential elements */
            .no-print {
                display: none !important;
            }

            /* Ensure background colors print */
            .tg th {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }

            /* Optimize header for print */
            .table-header img {
                height: 60px !important; /* Smaller logo */
            }

            /* Reduce spacing between sections */
            .tg {
                margin-top: 0.5rem !important;
            }

            /* Optimize footer spacing */
            .footer-text {
                margin-top: 0.5rem !important;
            }
        }
    </style>
</head>

<body>

    <table class="table-header"> <!-- Use specific class for header table -->
        <tbody>
            <tr>
                <td class="header-logo" width="20%"> <!-- Reduced width for more space -->
                    <img src="https://www.institut-imsaa.com/image/logo/logo2-removebg-preview.png" alt="logo IMSAA"
                        height="60"> <!-- Reduced height for tighter layout -->
                </td>
                <td class="header-info"> <!-- Use specific class -->
                    <h4 class="text-uppercase cool-gray mb-0"> <!-- Removed strong, adjusted margin -->
                        <span class="red">I</span>NSTITUT DE <span class="red">M</span>ANAGEMENT ET
                            DES <span class="red">S</span>CIENCES <span class="red">A</span>PPLIQUEES <br>D'<span
                                class="red">A</span>NTSIRANANA
                    </h4>
                    <p class="mb-0">DIRECTION GENERALE</p>
                    <p class="mb-0">DIRECTION DES ETUDES DE LA RECHERCHE ET DE L’ASSURANCE QUALITE</p>
                    <p class="mb-0">Domaine : {{ $parcours->first()->mention->domaine->nom }}</p>
                </td>
            </tr>
        </tbody>
    </table>

    <div class="text-center" style="margin-top: 1rem; margin-bottom: 1rem;"> <!-- Wrap title section -->
        @if (!empty($notes))
            <strong style="font-size: 1.1rem;">Liste des résultats en {{ $niveau->nom }} par ordre de mérite</strong>
            <p style="margin-top: 0.5rem; margin-bottom: 0.2rem;">Semestres :</p>
            <ul style="list-style: none; padding-left: 0;">
                @foreach($semestres as $semestre)
                    <li style="display: inline-block; margin-right: 10px;">{{ $semestre->nom }}</li>
                @endforeach
            </ul>
            <p style="margin-top: 0.5rem; margin-bottom: 0.2rem;">Parcours :</p>
            <ul style="list-style: none; padding-left: 0;">
                @foreach($parcours as $parcour)
                     <li style="display: inline-block; margin-right: 10px;">{{ $parcour->nom }}</li>
                @endforeach
            </ul>
            <p style="margin-top: 0.5rem; margin-bottom: 0.5rem;">Année universitaire : {{ $annee->nom }}</p>
        @else
            <p>Pas de résultat disponible.</p>
        @endif
        {{-- <strong>Résultat {{ $results[0]['semestre'] }} du parcours {{ $results[0]['parcours'] }} {{ $results[0]['niveau'] }}</strong> --}}
    </div>

    <table class="tg">
        <thead>
            <tr>
                <th class="tg-cly1" width="8%">RANG</th> <!-- Reduced width -->
                <th class="tg-cly1" width="62%">NOM ET PRENOMS</th> <!-- Adjusted width -->
                <th class="tg-cly1" width="20%">MENTION</th> <!-- Reduced width -->
            </tr>
        </thead>
        <tbody>
            @forelse ($notes as $result)
                <tr> <!-- Optimized row -->
                    <td class="tg-zr06 text-center">{{ $loop->iteration }}</td>
                    <td class="tg-zr06">
                        {{ $result["nom"] }} {{ $result["prenom"] }}
                    </td>
                    <td class="tg-zr06 text-center">
                        {{ $result["mention"] }}
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center"> <!-- Updated colspan -->
                        <p class="mb-0">
                            Impossible de générer. Pas encore de note ajoutée !
                        </p>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="footer-text"> <!-- Combine footer text for tighter layout -->
        <p style="margin-bottom: 0.2rem;">Arrêté la présente liste au nombre de {{ count($notes) }} étudiant(s).</p>
        <p style="margin-bottom: 0.5rem;">Fait à Antsiranana, le {{ \Carbon\Carbon::now()->locale('fr')->isoFormat('DD MMMM YYYY') }}</p>
    </div>
    <!-- Optimized signature section -->
    <table style="width: 100%; margin-top: 1rem; border: none;">
        <tr style="border: none;">
            <td style="width: 50%; text-align: center; border: none; font-size: 9px;"></td>
            <td style="width: 50%; text-align: center; border: none; font-size: 9px;">La Direction Générale</td>
        </tr>
    </table>

</body>

</html>
