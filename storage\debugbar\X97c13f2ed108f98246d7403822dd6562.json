{"__meta": {"id": "X97c13f2ed108f98246d7403822dd6562", "datetime": "2025-07-15 16:19:57", "utime": **********.154414, "method": "POST", "uri": "/livewire/message/result", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[16:19:57] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.126741, "collector": "log"}, {"message": "[16:19:57] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 9\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Result.php:179\r\n#18 \\app\\Http\\Livewire\\Result.php:150\r\n#19 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#20 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#23 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#24 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#25 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#26 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#27 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#28 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:776\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:740\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.127038, "collector": "log"}, {"message": "[16:19:57] LOG.info: Model: App\\Models\\Matiere\r\nRelation: App\\Models\\Note\r\nNum-Called: 27\r\nCall-Stack:\r\n#22 \\app\\Http\\Livewire\\Result.php:179\r\n#23 \\app\\Http\\Livewire\\Result.php:150\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#26 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#27 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#29 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#30 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#31 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#32 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.151487, "collector": "log"}, {"message": "[16:19:57] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 14\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Result.php:179\r\n#18 \\app\\Http\\Livewire\\Result.php:150\r\n#19 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#20 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#23 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#24 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#25 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#26 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#27 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#28 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:776\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:740\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.151839, "collector": "log"}, {"message": "[16:19:57] LOG.info: Model: App\\Models\\Matiere\r\nRelation: App\\Models\\Note\r\nNum-Called: 42\r\nCall-Stack:\r\n#22 \\app\\Http\\Livewire\\Result.php:179\r\n#23 \\app\\Http\\Livewire\\Result.php:150\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#26 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#27 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#29 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#30 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#31 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#32 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.152153, "collector": "log"}]}, "time": {"start": 1752585589.451214, "end": **********.154474, "duration": 7.7032599449157715, "duration_str": "7.7s", "measures": [{"label": "Booting", "start": 1752585589.451214, "relative_start": 0, "end": 1752585590.498358, "relative_end": 1752585590.498358, "duration": 1.0471439361572266, "duration_str": "1.05s", "params": [], "collector": null}, {"label": "Application", "start": 1752585590.499027, "relative_start": 1.0478129386901855, "end": **********.154478, "relative_end": 4.0531158447265625e-06, "duration": 6.655451059341431, "duration_str": "6.66s", "params": [], "collector": null}]}, "memory": {"peak_usage": 29045480, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.deraq.resultat.index (\\resources\\views\\livewire\\deraq\\resultat\\index.blade.php)", "param_count": 14, "params": ["parcours", "niveaux", "semestres", "annees", "livewireLayout", "errors", "_instance", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/index.blade.php&line=0"}, {"name": "livewire.deraq.resultat.liste (\\resources\\views\\livewire\\deraq\\resultat\\liste.blade.php)", "param_count": 16, "params": ["__env", "app", "errors", "_instance", "parcours", "niveaux", "semestres", "annees", "livewireLayout", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/liste.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 121, "nb_failed_statements": 0, "accumulated_duration": 4.6017899999999985, "accumulated_duration_str": "4.6s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00592, "duration_str": "5.92ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 0.129}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 121}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.02612, "duration_str": "26.12ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:121", "connection": "imsaaapp", "start_percent": 0.129, "width_percent": 0.568}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 124}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.029079999999999998, "duration_str": "29.08ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:124", "connection": "imsaaapp", "start_percent": 0.696, "width_percent": 0.632}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 127}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01066, "duration_str": "10.66ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:127", "connection": "imsaaapp", "start_percent": 1.328, "width_percent": 0.232}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 130}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Livewire\\Result.php:130", "connection": "imsaaapp", "start_percent": 1.56, "width_percent": 0.017}, {"sql": "select `id`, `nom`, `prenom` from `users` where exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `niveau_id` = '3' and `annee_universitaire_id` = '5' and `parcour_id` in ('1', '3') and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "5", "1", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 147}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07001, "duration_str": "70.01ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:147", "connection": "imsaaapp", "start_percent": 1.577, "width_percent": 1.521}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 349 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 349 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 349 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "349", "1", "349", "2", "349", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02016, "duration_str": "20.16ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.099, "width_percent": 0.438}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.537, "width_percent": 0.031}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 349 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "349"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0017800000000000001, "duration_str": "1.78ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.567, "width_percent": 0.039}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 349 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "349"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.606, "width_percent": 0.03}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 349 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "349"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00158, "duration_str": "1.58ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.636, "width_percent": 0.034}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "188", "1", "188", "2", "188", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0046, "duration_str": "4.6ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.67, "width_percent": 0.1}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00198, "duration_str": "1.98ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.77, "width_percent": 0.043}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.007730000000000001, "duration_str": "7.73ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.813, "width_percent": 0.168}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.028, "duration_str": "28ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 3.981, "width_percent": 0.608}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 4.59, "width_percent": 0.033}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "189", "1", "189", "2", "189", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00467, "duration_str": "4.67ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 4.622, "width_percent": 0.101}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 4.724, "width_percent": 0.027}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.026019999999999998, "duration_str": "26.02ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 4.751, "width_percent": 0.565}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.023469999999999998, "duration_str": "23.47ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 5.316, "width_percent": 0.51}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01986, "duration_str": "19.86ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 5.826, "width_percent": 0.432}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "192", "1", "192", "2", "192", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.013710000000000002, "duration_str": "13.71ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 6.258, "width_percent": 0.298}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0175, "duration_str": "17.5ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 6.555, "width_percent": 0.38}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.043340000000000004, "duration_str": "43.34ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 6.936, "width_percent": 0.942}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09340000000000001, "duration_str": "93.4ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 7.878, "width_percent": 2.03}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.056960000000000004, "duration_str": "56.96ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 9.907, "width_percent": 1.238}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "193", "1", "193", "2", "193", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08685999999999999, "duration_str": "86.86ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 11.145, "width_percent": 1.888}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.17486000000000002, "duration_str": "175ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 13.033, "width_percent": 3.8}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0841, "duration_str": "84.1ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 16.832, "width_percent": 1.828}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 18.66, "width_percent": 0.028}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0016799999999999999, "duration_str": "1.68ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 18.688, "width_percent": 0.037}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "194", "1", "194", "2", "194", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00487, "duration_str": "4.87ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 18.724, "width_percent": 0.106}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 18.83, "width_percent": 0.024}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0358, "duration_str": "35.8ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 18.854, "width_percent": 0.778}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0394, "duration_str": "39.4ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 19.632, "width_percent": 0.856}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03513, "duration_str": "35.13ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 20.488, "width_percent": 0.763}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "195", "1", "195", "2", "195", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0597, "duration_str": "59.7ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 21.252, "width_percent": 1.297}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0023599999999999997, "duration_str": "2.36ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 22.549, "width_percent": 0.051}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 22.6, "width_percent": 0.034}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01107, "duration_str": "11.07ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 22.634, "width_percent": 0.241}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01718, "duration_str": "17.18ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 22.875, "width_percent": 0.373}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "198", "1", "198", "2", "198", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0059299999999999995, "duration_str": "5.93ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 23.248, "width_percent": 0.129}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 23.377, "width_percent": 0.025}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02002, "duration_str": "20.02ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 23.402, "width_percent": 0.435}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.011869999999999999, "duration_str": "11.87ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 23.837, "width_percent": 0.258}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0112, "duration_str": "11.2ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 24.095, "width_percent": 0.243}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "199", "1", "199", "2", "199", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00429, "duration_str": "4.29ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 24.339, "width_percent": 0.093}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.028480000000000002, "duration_str": "28.48ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 24.432, "width_percent": 0.619}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.038689999999999995, "duration_str": "38.69ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 25.051, "width_percent": 0.841}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01211, "duration_str": "12.11ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 25.892, "width_percent": 0.263}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00819, "duration_str": "8.19ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 26.155, "width_percent": 0.178}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "200", "1", "200", "2", "200", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.015880000000000002, "duration_str": "15.88ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 26.333, "width_percent": 0.345}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07889, "duration_str": "78.89ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 26.678, "width_percent": 1.714}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09434999999999999, "duration_str": "94.35ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 28.392, "width_percent": 2.05}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.052219999999999996, "duration_str": "52.22ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 30.443, "width_percent": 1.135}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03786, "duration_str": "37.86ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 31.577, "width_percent": 0.823}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "201", "1", "201", "2", "201", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01728, "duration_str": "17.28ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 32.4, "width_percent": 0.376}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02623, "duration_str": "26.23ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 32.776, "width_percent": 0.57}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01239, "duration_str": "12.39ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 33.346, "width_percent": 0.269}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.013980000000000001, "duration_str": "13.98ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 33.615, "width_percent": 0.304}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.026350000000000002, "duration_str": "26.35ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 33.919, "width_percent": 0.573}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "203", "1", "203", "2", "203", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0052, "duration_str": "5.2ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 34.491, "width_percent": 0.113}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02361, "duration_str": "23.61ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 34.604, "width_percent": 0.513}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09445, "duration_str": "94.45ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 35.117, "width_percent": 2.052}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07159, "duration_str": "71.59ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 37.17, "width_percent": 1.556}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.037899999999999996, "duration_str": "37.9ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 38.725, "width_percent": 0.824}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "204", "1", "204", "2", "204", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0703, "duration_str": "70.3ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 39.549, "width_percent": 1.528}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04461, "duration_str": "44.61ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 41.077, "width_percent": 0.969}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04992, "duration_str": "49.92ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 42.046, "width_percent": 1.085}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01828, "duration_str": "18.28ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 43.131, "width_percent": 0.397}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.037829999999999996, "duration_str": "37.83ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 43.528, "width_percent": 0.822}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "207", "1", "207", "2", "207", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.19343000000000002, "duration_str": "193ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 44.35, "width_percent": 4.203}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05724, "duration_str": "57.24ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 48.553, "width_percent": 1.244}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.022269999999999998, "duration_str": "22.27ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 49.797, "width_percent": 0.484}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08147, "duration_str": "81.47ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 50.281, "width_percent": 1.77}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03435, "duration_str": "34.35ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 52.052, "width_percent": 0.746}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 208 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 208 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 208 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "208", "1", "208", "2", "208", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06470000000000001, "duration_str": "64.7ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 52.798, "width_percent": 1.406}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06881, "duration_str": "68.81ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 54.204, "width_percent": 1.495}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 208 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10401, "duration_str": "104ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 55.699, "width_percent": 2.26}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 208 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03953, "duration_str": "39.53ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 57.96, "width_percent": 0.859}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 208 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03536, "duration_str": "35.36ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 58.819, "width_percent": 0.768}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 222 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 222 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 222 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "222", "1", "222", "2", "222", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05493, "duration_str": "54.93ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 59.587, "width_percent": 1.194}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03787, "duration_str": "37.87ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 60.781, "width_percent": 0.823}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 222 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "222"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.023190000000000002, "duration_str": "23.19ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 61.604, "width_percent": 0.504}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 222 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "222"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02613, "duration_str": "26.13ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 62.108, "width_percent": 0.568}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 222 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "222"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07476999999999999, "duration_str": "74.77ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 62.675, "width_percent": 1.625}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 223 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 223 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 223 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "223", "1", "223", "2", "223", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.12390000000000001, "duration_str": "124ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 64.3, "width_percent": 2.692}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05971, "duration_str": "59.71ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 66.993, "width_percent": 1.298}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 223 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "223"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06531, "duration_str": "65.31ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 68.29, "width_percent": 1.419}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 223 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "223"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04065, "duration_str": "40.65ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 69.709, "width_percent": 0.883}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 223 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "223"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.035590000000000004, "duration_str": "35.59ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 70.593, "width_percent": 0.773}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 224 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 224 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 224 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "224", "1", "224", "2", "224", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04494, "duration_str": "44.94ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 71.366, "width_percent": 0.977}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03082, "duration_str": "30.82ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 72.343, "width_percent": 0.67}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 224 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "224"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0126, "duration_str": "12.6ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 73.012, "width_percent": 0.274}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 224 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "224"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.20307, "duration_str": "203ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 73.286, "width_percent": 4.413}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 224 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "224"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06752, "duration_str": "67.52ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 77.699, "width_percent": 1.467}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 225 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 225 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 225 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "225", "1", "225", "2", "225", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.015880000000000002, "duration_str": "15.88ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 79.166, "width_percent": 0.345}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04331, "duration_str": "43.31ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 79.511, "width_percent": 0.941}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 225 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "225"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02428, "duration_str": "24.28ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 80.453, "width_percent": 0.528}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 225 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "225"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0584, "duration_str": "58.4ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 80.98, "width_percent": 1.269}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 225 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "225"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04467, "duration_str": "44.67ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 82.249, "width_percent": 0.971}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 226 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 226 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 226 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "226", "1", "226", "2", "226", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04347, "duration_str": "43.47ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 83.22, "width_percent": 0.945}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04129, "duration_str": "41.29ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 84.165, "width_percent": 0.897}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 226 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "226"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04428, "duration_str": "44.28ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 85.062, "width_percent": 0.962}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 226 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "226"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.038759999999999996, "duration_str": "38.76ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 86.024, "width_percent": 0.842}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 226 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "226"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.045810000000000003, "duration_str": "45.81ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 86.866, "width_percent": 0.995}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "227", "1", "227", "2", "227", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01035, "duration_str": "10.35ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 87.862, "width_percent": 0.225}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01809, "duration_str": "18.09ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 88.087, "width_percent": 0.393}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00553, "duration_str": "5.53ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 88.48, "width_percent": 0.12}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 88.6, "width_percent": 0.03}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00322, "duration_str": "3.22ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 88.63, "width_percent": 0.07}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 228 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 228 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 228 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "228", "1", "228", "2", "228", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04571, "duration_str": "45.71ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 88.7, "width_percent": 0.993}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03162, "duration_str": "31.62ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 89.693, "width_percent": 0.687}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 228 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "228"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.011349999999999999, "duration_str": "11.35ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 90.38, "width_percent": 0.247}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 228 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "228"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04489, "duration_str": "44.89ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 90.627, "width_percent": 0.975}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 228 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "228"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05636, "duration_str": "56.36ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 91.603, "width_percent": 1.225}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 393 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 393 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 393 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "393", "1", "393", "2", "393", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06014, "duration_str": "60.14ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 92.827, "width_percent": 1.307}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08676, "duration_str": "86.76ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 94.134, "width_percent": 1.885}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 393 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "393"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03796, "duration_str": "37.96ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 96.02, "width_percent": 0.825}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 393 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "393"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05371, "duration_str": "53.71ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 96.844, "width_percent": 1.167}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 393 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "393"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 179}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 150}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0915, "duration_str": "91.5ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:179", "connection": "imsaaapp", "start_percent": 98.012, "width_percent": 1.988}]}, "models": {"data": {"App\\Models\\Note": 359, "App\\Models\\Matiere": 115, "App\\Models\\Ue": 69, "App\\Models\\AnneeUniversitaire": 6, "App\\Models\\Semestre": 10, "App\\Models\\Niveau": 5, "App\\Models\\Parcour": 24, "App\\Models\\User": 24}, "count": 612}, "livewire": {"data": {"result #RKn4IezkV1OrnPcSb5JP": "array:5 [\n  \"data\" => array:7 [\n    \"newResults\" => array:4 [\n      \"parcour_id\" => array:2 [\n        0 => \"1\"\n        1 => \"3\"\n      ]\n      \"niveau_id\" => \"3\"\n      \"semestre_id\" => array:1 [\n        0 => \"5\"\n      ]\n      \"annee_universitaire_id\" => \"5\"\n    ]\n    \"notes\" => array:23 [\n      0 => array:6 [\n        \"nom\" => \"TSARALAZA \"\n        \"prenom\" => \"Vonindraozy Anouchka Norah\"\n        \"moy_raw\" => 15.083333333333\n        \"moy\" => \"15.08\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 1\n      ]\n      1 => array:6 [\n        \"nom\" => \"EMILE\"\n        \"prenom\" => \"Yohan SOARAVO\"\n        \"moy_raw\" => 14.333333333333\n        \"moy\" => \"14.33\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 2\n      ]\n      2 => array:6 [\n        \"nom\" => \"JAOSOLO\"\n        \"prenom\" => \"Youssouf Sahara\"\n        \"moy_raw\" => 14.041666666667\n        \"moy\" => \"14.04\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 3\n      ]\n      3 => array:6 [\n        \"nom\" => \"RAKOTONDRAFARA \"\n        \"prenom\" => \"Mendrika <PERSON>loina\"\n        \"moy_raw\" => 13.916666666667\n        \"moy\" => \"13.92\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 4\n      ]\n      4 => array:6 [\n        \"nom\" => \"ZAFY \"\n        \"prenom\" => \"Angela Nathalie\"\n        \"moy_raw\" => 13.708333333333\n        \"moy\" => \"13.71\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 5\n      ]\n      5 => array:6 [\n        \"nom\" => \"FLORICIA \"\n        \"prenom\" => \"Marie Daniela\"\n        \"moy_raw\" => 13.0625\n        \"moy\" => \"13.06\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 6\n      ]\n      6 => array:6 [\n        \"nom\" => \"LOVA\"\n        \"prenom\" => \"Faniva Tsimaholy\"\n        \"moy_raw\" => 12.916666666667\n        \"moy\" => \"12.92\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 7\n      ]\n      7 => array:6 [\n        \"nom\" => \"RANARY \"\n        \"prenom\" => \"Henrista Larina\"\n        \"moy_raw\" => 12.833333333333\n        \"moy\" => \"12.83\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 8\n      ]\n      8 => array:6 [\n        \"nom\" => \"RAHERIMANANA\"\n        \"prenom\" => \"Laurencia\"\n        \"moy_raw\" => 12.208333333333\n        \"moy\" => \"12.21\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 9\n      ]\n      9 => array:6 [\n        \"nom\" => \"GAMILA \"\n        \"prenom\" => \"Velonjara \"\n        \"moy_raw\" => 11.833333333333\n        \"moy\" => \"11.83\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 10\n      ]\n      10 => array:6 [\n        \"nom\" => \"RAVELOMIRANA\"\n        \"prenom\" => \"Lauriana Sylvanna\"\n        \"moy_raw\" => 11.458333333333\n        \"moy\" => \"11.46\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 11\n      ]\n      11 => array:6 [\n        \"nom\" => \"RAZAFINDRAZANANY\"\n        \"prenom\" => \"Marnella\"\n        \"moy_raw\" => 11.041666666667\n        \"moy\" => \"11.04\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 12\n      ]\n      12 => array:6 [\n        \"nom\" => \"SOA \"\n        \"prenom\" => \"Philipine Causta\"\n        \"moy_raw\" => 10.875\n        \"moy\" => \"10.88\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 13\n      ]\n      13 => array:6 [\n        \"nom\" => \"RAZAFIMBELO\"\n        \"prenom\" => \"Marie Stella Philippe\"\n        \"moy_raw\" => 10.833333333333\n        \"moy\" => \"10.83\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 14\n      ]\n      14 => array:6 [\n        \"nom\" => \"RAZAFY \"\n        \"prenom\" => \"Marie Antoniesca\"\n        \"moy_raw\" => 10.541666666667\n        \"moy\" => \"10.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 15\n      ]\n      15 => array:6 [\n        \"nom\" => \"TOTOFENO\"\n        \"prenom\" => \" Leticia Caprima\"\n        \"moy_raw\" => 9.875\n        \"moy\" => \"9.88\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 16\n      ]\n      16 => array:6 [\n        \"nom\" => \"TREFINDRAZANA \"\n        \"prenom\" => \"Ricina\"\n        \"moy_raw\" => 9.7083333333333\n        \"moy\" => \"9.71\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 17\n      ]\n      17 => array:6 [\n        \"nom\" => \"LASALMONIE\"\n        \"prenom\" => \"Joana Richina\"\n        \"moy_raw\" => 9.5\n        \"moy\" => \"9.50\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 18\n      ]\n      18 => array:6 [\n        \"nom\" => \"MANOROSOA\"\n        \"prenom\" => \"Aboudou Sandra\"\n        \"moy_raw\" => 8.375\n        \"moy\" => \"8.38\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 19\n      ]\n      19 => array:6 [\n        \"nom\" => \"SAGNITRY\"\n        \"prenom\" => \"Rachida Adrianah\"\n        \"moy_raw\" => 8.2083333333333\n        \"moy\" => \"8.21\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 20\n      ]\n      20 => array:6 [\n        \"nom\" => \"IASILANY\"\n        \"prenom\" => \"Prisco\"\n        \"moy_raw\" => 7.5416666666667\n        \"moy\" => \"7.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 21\n      ]\n      21 => array:6 [\n        \"nom\" => \"MAMORY\"\n        \"prenom\" => \"Andy Stanley\"\n        \"moy_raw\" => 5.9583333333333\n        \"moy\" => \"5.96\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 22\n      ]\n      22 => array:6 [\n        \"nom\" => \"HOLIFARA\"\n        \"prenom\" => \"Andrianina Jaura\"\n        \"moy_raw\" => 3.3333333333333\n        \"moy\" => \"3.33\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 23\n      ]\n    ]\n    \"showResults\" => true\n    \"current_parcours\" => Illuminate\\Database\\Eloquent\\Collection {#3113\n      #items: array:2 [\n        0 => App\\Models\\Parcour {#1425\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Parcour {#1720\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 3\n            \"sigle\" => \"CM\"\n            \"nom\" => \"Communication et Marketing\"\n            \"mention_id\" => 5\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 3\n            \"sigle\" => \"CM\"\n            \"nom\" => \"Communication et Marketing\"\n            \"mention_id\" => 5\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1765\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_semestres\" => Illuminate\\Database\\Eloquent\\Collection {#3731\n      #items: array:1 [\n        0 => App\\Models\\Semestre {#1782\n          #connection: \"mysql\"\n          #table: \"semestres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"niveau_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1797\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n  ]\n  \"name\" => \"result\"\n  \"view\" => \"livewire.deraq.resultat.index\"\n  \"component\" => \"App\\Http\\Livewire\\Result\"\n  \"id\" => \"RKn4IezkV1OrnPcSb5JP\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/resultat\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752585532\n]"}, "request": {"path_info": "/livewire/message/result", "status_code": "<pre class=sf-dump id=sf-dump-1829668243 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1829668243\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1151775604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1151775604\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1797814906 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">RKn4IezkV1OrnPcSb5JP</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">result</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"21 characters\">pedagogiques/resultat</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">172f213c</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newResults</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcour_id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>niveau_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>5</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>annee_universitaire_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>showResults</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_semestres</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">8b84069f64cb2b9cd41d9a9bf934e921c94740148758b9c93792bda728a8f631</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">icucl</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">generateResults</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797814906\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1909592604 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">619</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imc2T3hMYUF5dGZMSmM5SmVnY0RIT0E9PSIsInZhbHVlIjoiZmlrdFpDNlBaVzBtK0J2TUNVOG04NEp5SnR4OVpONUc3NVYzYXZ4Y3Y5YyttaTY1VnhqR1J1YzJqRW93U0VHVmViTWFmd3laNHNMTXBYS1YrMjkvT2VRV1RNSktuaFpjSng4dXlrUnNweE4rZG9JWUxWNFV3cjBLTWZodUg5WFIiLCJtYWMiOiIwMTQyZWRkZjQ5Zjc5Nzg4MzkwOGUyYmVlMDcyYjI5ZGU5Y2U0MDBhY2Y3NzRkNDFlZGMzMmZiYTRhYWQzZjk5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Inpvdjgvay9yR2ExTEllVEwrQndXeUE9PSIsInZhbHVlIjoiQ0NUVjRpY214STFVYnVONEpPTHhqQmh4bXgwbFIrSE4wcU5zd1hqU0FVdmFKRnc4NmNiS3dxZXZVcDhIVEtPY2p1bHZ3dmV4S1V6elVEMlUrYjdkbkN1aE11ZFlNZ29reEI2QXFHNnQ4Ukg2MkhOMzJoR2FkeXNkN0dSeWFza0MiLCJtYWMiOiIyZmJmZDI3NWViNjkzZWQ2Yjk3ZjkyZjQyZDlhMzFhZWNlYjNjNGI3NTQ4Y2IwZGY2NWM3YTAyOGI2NWQzMTM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909592604\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-488846240 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57083</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"34 characters\">/index.php/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">619</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">619</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imc2T3hMYUF5dGZMSmM5SmVnY0RIT0E9PSIsInZhbHVlIjoiZmlrdFpDNlBaVzBtK0J2TUNVOG04NEp5SnR4OVpONUc3NVYzYXZ4Y3Y5YyttaTY1VnhqR1J1YzJqRW93U0VHVmViTWFmd3laNHNMTXBYS1YrMjkvT2VRV1RNSktuaFpjSng4dXlrUnNweE4rZG9JWUxWNFV3cjBLTWZodUg5WFIiLCJtYWMiOiIwMTQyZWRkZjQ5Zjc5Nzg4MzkwOGUyYmVlMDcyYjI5ZGU5Y2U0MDBhY2Y3NzRkNDFlZGMzMmZiYTRhYWQzZjk5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Inpvdjgvay9yR2ExTEllVEwrQndXeUE9PSIsInZhbHVlIjoiQ0NUVjRpY214STFVYnVONEpPTHhqQmh4bXgwbFIrSE4wcU5zd1hqU0FVdmFKRnc4NmNiS3dxZXZVcDhIVEtPY2p1bHZ3dmV4S1V6elVEMlUrYjdkbkN1aE11ZFlNZ29reEI2QXFHNnQ4Ukg2MkhOMzJoR2FkeXNkN0dSeWFza0MiLCJtYWMiOiIyZmJmZDI3NWViNjkzZWQ2Yjk3ZjkyZjQyZDlhMzFhZWNlYjNjNGI3NTQ4Y2IwZGY2NWM3YTAyOGI2NWQzMTM0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752585589.4512</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752585589</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488846240\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-548099586 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EeIhqoegAUgFKdYeTg2gXSMBO9eZTPzhJ6gMO0UM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548099586\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-753045253 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 13:19:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik9Sb250MzNmajJqNFdFZFowSFhpUEE9PSIsInZhbHVlIjoiOEhYUGlxbENzMGJrWkVlNlRwSCs4ZXdYRTFLdTVFU1hzdGRNM205YU9DLzRjNG5rRzJHbVU1cG1JQnhtcVM4akRobVJ1c1FpdDZSOVFadVRLYVQ3UVQ2aHlZTnN5NHJqOWt2VEJGdkFobnRNdzRZUitXQk1IbmR1ODJUbzlJUUUiLCJtYWMiOiJkNjY2N2MyNWRmZTRjMTNjY2ViNmQzYmNlZGFjNWVkZWY3MmI2NGJmNTQ3M2ZiYjBiNzI2MGRjMDNiYTdhZDA0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 15:19:56 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IktCci9CYmVLcG1TSXN6Umh3QWhoRWc9PSIsInZhbHVlIjoiZnpBaGNDaTBsbklBVWxUNjZtZWpFbUlBQmZ4ZkN2aGU4ZWNUN3F6OUpXeGRQZnIyYjRCMmRoQVo5c1BGY25Wc2NoaU94emppc0hUWEtOME5vWGJ2ekdnaC9mTlR3dUFpTXR6emlQaUlzY2NUbFdzYzQyeGdiSmJRUzdMTDI4aTEiLCJtYWMiOiI0ZjRjODdiNGRkNGI3OTIwMmNhNmEzNDdkNzE2ZjIxMTliNDY4ZTVhODlmYTI5ZGRhZTU1ZjlhZGY0ZmRkODJhIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 15:19:56 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik9Sb250MzNmajJqNFdFZFowSFhpUEE9PSIsInZhbHVlIjoiOEhYUGlxbENzMGJrWkVlNlRwSCs4ZXdYRTFLdTVFU1hzdGRNM205YU9DLzRjNG5rRzJHbVU1cG1JQnhtcVM4akRobVJ1c1FpdDZSOVFadVRLYVQ3UVQ2aHlZTnN5NHJqOWt2VEJGdkFobnRNdzRZUitXQk1IbmR1ODJUbzlJUUUiLCJtYWMiOiJkNjY2N2MyNWRmZTRjMTNjY2ViNmQzYmNlZGFjNWVkZWY3MmI2NGJmNTQ3M2ZiYjBiNzI2MGRjMDNiYTdhZDA0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 15:19:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IktCci9CYmVLcG1TSXN6Umh3QWhoRWc9PSIsInZhbHVlIjoiZnpBaGNDaTBsbklBVWxUNjZtZWpFbUlBQmZ4ZkN2aGU4ZWNUN3F6OUpXeGRQZnIyYjRCMmRoQVo5c1BGY25Wc2NoaU94emppc0hUWEtOME5vWGJ2ekdnaC9mTlR3dUFpTXR6emlQaUlzY2NUbFdzYzQyeGdiSmJRUzdMTDI4aTEiLCJtYWMiOiI0ZjRjODdiNGRkNGI3OTIwMmNhNmEzNDdkNzE2ZjIxMTliNDY4ZTVhODlmYTI5ZGRhZTU1ZjlhZGY0ZmRkODJhIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 15:19:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753045253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-447366848 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752585532</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447366848\", {\"maxDepth\":0})</script>\n"}}