{"__meta": {"id": "X433f3821a95150be0b5cddb455bc61d9", "datetime": "2025-07-15 16:40:15", "utime": 1752586815.600816, "method": "POST", "uri": "/livewire/message/result", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:40:09] LOG.warning: mb_convert_encoding(): Handling HTML entities via mbstring is deprecated; use htmlspecialchars, htmlentities, or mb_encode_numericentity/mb_decode_numericentity instead in C:\\xampp\\htdocs\\ImsaaProject\\app\\Http\\Livewire\\Result.php on line 305", "message_html": null, "is_string": false, "label": "warning", "time": 1752586809.362277, "collector": "log"}]}, "time": {"start": 1752586807.075933, "end": 1752586815.600855, "duration": 8.524922132492065, "duration_str": "8.52s", "measures": [{"label": "Booting", "start": 1752586807.075933, "relative_start": 0, "end": 1752586808.448823, "relative_end": 1752586808.448823, "duration": 1.3728899955749512, "duration_str": "1.37s", "params": [], "collector": null}, {"label": "Application", "start": 1752586808.450458, "relative_start": 1.3745250701904297, "end": 1752586815.600859, "relative_end": 3.814697265625e-06, "duration": 7.150400876998901, "duration_str": "7.15s", "params": [], "collector": null}]}, "memory": {"peak_usage": 38571680, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "pdf.resultat (\\resources\\views\\pdf\\resultat.blade.php)", "param_count": 5, "params": ["notes", "parcours", "niveau", "semestres", "annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/pdf/resultat.blade.php&line=0"}, {"name": "livewire.deraq.resultat.index (\\resources\\views\\livewire\\deraq\\resultat\\index.blade.php)", "param_count": 14, "params": ["parcours", "niveaux", "semestres", "annees", "livewireLayout", "errors", "_instance", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/index.blade.php&line=0"}, {"name": "livewire.deraq.resultat.liste (\\resources\\views\\livewire\\deraq\\resultat\\liste.blade.php)", "param_count": 16, "params": ["__env", "app", "errors", "_instance", "parcours", "niveaux", "semestres", "annees", "livewireLayout", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/liste.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.41397, "accumulated_duration_str": "414ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.08034999999999999, "duration_str": "80.35ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 19.41}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 19.41, "width_percent": 0.213}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 19.622, "width_percent": 0.188}, {"sql": "select * from `semestres` where `semestres`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00263, "duration_str": "2.63ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 19.811, "width_percent": 0.635}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.07118000000000001, "duration_str": "71.18ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 20.446, "width_percent": 17.194}, {"sql": "select * from `mentions` where `mentions`.`id` = 4 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "ae43b2be1dbf2c5dbf48b267409c276e0aa335ae", "line": 360}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "duration": 0.05001, "duration_str": "50.01ms", "stmt_id": "view::ae43b2be1dbf2c5dbf48b267409c276e0aa335ae:360", "connection": "imsaaapp", "start_percent": 37.64, "width_percent": 12.081}, {"sql": "select * from `domaines` where `domaines`.`id` = 1 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "ae43b2be1dbf2c5dbf48b267409c276e0aa335ae", "line": 360}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "duration": 0.019289999999999998, "duration_str": "19.29ms", "stmt_id": "view::ae43b2be1dbf2c5dbf48b267409c276e0aa335ae:360", "connection": "imsaaapp", "start_percent": 49.721, "width_percent": 4.66}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 68}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07123, "duration_str": "71.23ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:68", "connection": "imsaaapp", "start_percent": 54.381, "width_percent": 17.207}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 69}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.03564, "duration_str": "35.64ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:69", "connection": "imsaaapp", "start_percent": 71.587, "width_percent": 8.609}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 70}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.024239999999999998, "duration_str": "24.24ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:70", "connection": "imsaaapp", "start_percent": 80.197, "width_percent": 5.855}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 71}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.05774, "duration_str": "57.74ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:71", "connection": "imsaaapp", "start_percent": 86.052, "width_percent": 13.948}]}, "models": {"data": {"App\\Models\\Domaine": 1, "App\\Models\\Mention": 1, "App\\Models\\AnneeUniversitaire": 7, "App\\Models\\Semestre": 11, "App\\Models\\Niveau": 6, "App\\Models\\Parcour": 26, "App\\Models\\User": 1}, "count": 53}, "livewire": {"data": {"result #RKn4IezkV1OrnPcSb5JP": "array:5 [\n  \"data\" => array:7 [\n    \"newResults\" => array:4 [\n      \"parcour_id\" => array:2 [\n        0 => \"1\"\n        1 => \"3\"\n      ]\n      \"niveau_id\" => \"3\"\n      \"semestre_id\" => array:1 [\n        0 => \"5\"\n      ]\n      \"annee_universitaire_id\" => \"5\"\n    ]\n    \"notes\" => array:23 [\n      0 => array:6 [\n        \"nom\" => \"TSARALAZA \"\n        \"prenom\" => \"Vonindraozy Anouchka Norah\"\n        \"moy_raw\" => 15.083333333333\n        \"moy\" => \"15.08\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 1\n      ]\n      1 => array:6 [\n        \"nom\" => \"EMILE\"\n        \"prenom\" => \"Yohan SOARAVO\"\n        \"moy_raw\" => 14.333333333333\n        \"moy\" => \"14.33\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 2\n      ]\n      2 => array:6 [\n        \"nom\" => \"JAOSOLO\"\n        \"prenom\" => \"Youssouf Sahara\"\n        \"moy_raw\" => 14.041666666667\n        \"moy\" => \"14.04\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 3\n      ]\n      3 => array:6 [\n        \"nom\" => \"RAKOTONDRAFARA \"\n        \"prenom\" => \"Mendrika <PERSON>loina\"\n        \"moy_raw\" => 13.916666666667\n        \"moy\" => \"13.92\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 4\n      ]\n      4 => array:6 [\n        \"nom\" => \"ZAFY \"\n        \"prenom\" => \"Angela Nathalie\"\n        \"moy_raw\" => 13.708333333333\n        \"moy\" => \"13.71\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 5\n      ]\n      5 => array:6 [\n        \"nom\" => \"FLORICIA \"\n        \"prenom\" => \"Marie Daniela\"\n        \"moy_raw\" => 13.0625\n        \"moy\" => \"13.06\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 6\n      ]\n      6 => array:6 [\n        \"nom\" => \"LOVA\"\n        \"prenom\" => \"Faniva Tsimaholy\"\n        \"moy_raw\" => 12.916666666667\n        \"moy\" => \"12.92\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 7\n      ]\n      7 => array:6 [\n        \"nom\" => \"RANARY \"\n        \"prenom\" => \"Henrista Larina\"\n        \"moy_raw\" => 12.833333333333\n        \"moy\" => \"12.83\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 8\n      ]\n      8 => array:6 [\n        \"nom\" => \"RAHERIMANANA\"\n        \"prenom\" => \"Laurencia\"\n        \"moy_raw\" => 12.208333333333\n        \"moy\" => \"12.21\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 9\n      ]\n      9 => array:6 [\n        \"nom\" => \"GAMILA \"\n        \"prenom\" => \"Velonjara \"\n        \"moy_raw\" => 11.833333333333\n        \"moy\" => \"11.83\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 10\n      ]\n      10 => array:6 [\n        \"nom\" => \"RAVELOMIRANA\"\n        \"prenom\" => \"Lauriana Sylvanna\"\n        \"moy_raw\" => 11.458333333333\n        \"moy\" => \"11.46\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 11\n      ]\n      11 => array:6 [\n        \"nom\" => \"RAZAFINDRAZANANY\"\n        \"prenom\" => \"Marnella\"\n        \"moy_raw\" => 11.041666666667\n        \"moy\" => \"11.04\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 12\n      ]\n      12 => array:6 [\n        \"nom\" => \"SOA \"\n        \"prenom\" => \"Philipine Causta\"\n        \"moy_raw\" => 10.875\n        \"moy\" => \"10.88\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 13\n      ]\n      13 => array:6 [\n        \"nom\" => \"RAZAFIMBELO\"\n        \"prenom\" => \"Marie Stella Philippe\"\n        \"moy_raw\" => 10.833333333333\n        \"moy\" => \"10.83\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 14\n      ]\n      14 => array:6 [\n        \"nom\" => \"RAZAFY \"\n        \"prenom\" => \"Marie Antoniesca\"\n        \"moy_raw\" => 10.541666666667\n        \"moy\" => \"10.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 15\n      ]\n      15 => array:6 [\n        \"nom\" => \"TOTOFENO\"\n        \"prenom\" => \" Leticia Caprima\"\n        \"moy_raw\" => 9.875\n        \"moy\" => \"9.88\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 16\n      ]\n      16 => array:6 [\n        \"nom\" => \"TREFINDRAZANA \"\n        \"prenom\" => \"Ricina\"\n        \"moy_raw\" => 9.7083333333333\n        \"moy\" => \"9.71\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 17\n      ]\n      17 => array:6 [\n        \"nom\" => \"LASALMONIE\"\n        \"prenom\" => \"Joana Richina\"\n        \"moy_raw\" => 9.5\n        \"moy\" => \"9.50\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 18\n      ]\n      18 => array:6 [\n        \"nom\" => \"MANOROSOA\"\n        \"prenom\" => \"Aboudou Sandra\"\n        \"moy_raw\" => 8.375\n        \"moy\" => \"8.38\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 19\n      ]\n      19 => array:6 [\n        \"nom\" => \"SAGNITRY\"\n        \"prenom\" => \"Rachida Adrianah\"\n        \"moy_raw\" => 8.2083333333333\n        \"moy\" => \"8.21\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 20\n      ]\n      20 => array:6 [\n        \"nom\" => \"IASILANY\"\n        \"prenom\" => \"Prisco\"\n        \"moy_raw\" => 7.5416666666667\n        \"moy\" => \"7.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 21\n      ]\n      21 => array:6 [\n        \"nom\" => \"MAMORY\"\n        \"prenom\" => \"Andy Stanley\"\n        \"moy_raw\" => 5.9583333333333\n        \"moy\" => \"5.96\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 22\n      ]\n      22 => array:6 [\n        \"nom\" => \"HOLIFARA\"\n        \"prenom\" => \"Andrianina Jaura\"\n        \"moy_raw\" => 3.3333333333333\n        \"moy\" => \"3.33\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 23\n      ]\n    ]\n    \"showResults\" => true\n    \"current_parcours\" => Illuminate\\Database\\Eloquent\\Collection {#3214\n      #items: array:2 [\n        0 => App\\Models\\Parcour {#1698\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"mention\" => App\\Models\\Mention {#1708\n              #connection: \"mysql\"\n              #table: \"mentions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:4 [\n                \"id\" => 4\n                \"nom\" => \"Tourisme et Hôteliers \"\n                \"domaine_id\" => 1\n                \"deleted_at\" => null\n              ]\n              #original: array:4 [\n                \"id\" => 4\n                \"nom\" => \"Tourisme et Hôteliers \"\n                \"domaine_id\" => 1\n                \"deleted_at\" => null\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"domaine\" => App\\Models\\Domaine {#1815\n                  #connection: \"mysql\"\n                  #table: \"domaines\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:3 [\n                    \"id\" => 1\n                    \"nom\" => \"Science de la société\"\n                    \"deleted_at\" => null\n                  ]\n                  #original: array:3 [\n                    \"id\" => 1\n                    \"nom\" => \"Science de la société\"\n                    \"deleted_at\" => null\n                  ]\n                  #changes: []\n                  #casts: array:1 [\n                    \"deleted_at\" => \"datetime\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dates: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: array:1 [\n                    0 => \"*\"\n                  ]\n                  #forceDeleting: false\n                }\n              ]\n              #touches: []\n              +timestamps: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"nom\"\n                1 => \"domaine_id\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Parcour {#1700\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 3\n            \"sigle\" => \"CM\"\n            \"nom\" => \"Communication et Marketing\"\n            \"mention_id\" => 5\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 3\n            \"sigle\" => \"CM\"\n            \"nom\" => \"Communication et Marketing\"\n            \"mention_id\" => 5\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1721\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_semestres\" => Illuminate\\Database\\Eloquent\\Collection {#2430\n      #items: array:1 [\n        0 => App\\Models\\Semestre {#1732\n          #connection: \"mysql\"\n          #table: \"semestres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"niveau_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1740\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n  ]\n  \"name\" => \"result\"\n  \"view\" => \"livewire.deraq.resultat.index\"\n  \"component\" => \"App\\Http\\Livewire\\Result\"\n  \"id\" => \"RKn4IezkV1OrnPcSb5JP\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/resultat\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752585532\n]"}, "request": {"path_info": "/livewire/message/result", "status_code": "<pre class=sf-dump id=sf-dump-193104875 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-193104875\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2078324164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2078324164\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-321748746 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">RKn4IezkV1OrnPcSb5JP</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">result</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"21 characters\">pedagogiques/resultat</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">b9213cc7</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newResults</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcour_id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>niveau_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>5</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>annee_universitaire_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-note>array:23</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">TSARALAZA </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Vonindraozy Anouchka Norah</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>15.083333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.08</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>1</span>\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">EMILE</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Yohan SOARAVO</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>14.333333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">14.33</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>2</span>\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">JAOSOLO</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Youssouf Sahara</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>14.041666666667</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">14.04</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>3</span>\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"15 characters\">RAKOTONDRAFARA </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Mendrika Koloina</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>13.916666666667</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13.92</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Assez-bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>4</span>\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ZAFY </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Angela Nathalie</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>13.708333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13.71</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Assez-bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>5</span>\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">FLORICIA </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Marie Daniela</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>13.0625</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">13.06</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Assez-bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>6</span>\n        </samp>]\n        <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">LOVA</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Faniva Tsimaholy</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>12.916666666667</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.92</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Assez-bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>7</span>\n        </samp>]\n        <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">RANARY </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Henrista Larina</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>12.833333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.83</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Assez-bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>8</span>\n        </samp>]\n        <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">RAHERIMANANA</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Laurencia</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>12.208333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.21</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Assez-bien</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>9</span>\n        </samp>]\n        <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">GAMILA </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Velonjara </span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>11.833333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.83</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>10</span>\n        </samp>]\n        <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">RAVELOMIRANA</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Lauriana Sylvanna</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>11.458333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.46</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>11</span>\n        </samp>]\n        <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">RAZAFINDRAZANANY</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Marnella</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>11.041666666667</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.04</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>12</span>\n        </samp>]\n        <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"4 characters\">SOA </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Philipine Causta</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>10.875</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.88</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>13</span>\n        </samp>]\n        <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"11 characters\">RAZAFIMBELO</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Marie Stella Philippe</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>10.833333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.83</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>14</span>\n        </samp>]\n        <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">RAZAFY </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Marie Antoniesca</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>10.541666666667</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.54</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>15</span>\n        </samp>]\n        <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">TOTOFENO</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"16 characters\"> Leticia Caprima</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>9.875</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9.88</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>16</span>\n        </samp>]\n        <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"14 characters\">TREFINDRAZANA </span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Ricina</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>9.7083333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9.71</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>17</span>\n        </samp>]\n        <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">LASALMONIE</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Joana Richina</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>9.5</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9.50</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>18</span>\n        </samp>]\n        <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">MANOROSOA</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Aboudou Sandra</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>8.375</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8.38</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>19</span>\n        </samp>]\n        <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">SAGNITRY</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Rachida Adrianah</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>8.2083333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8.21</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>20</span>\n        </samp>]\n        <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">IASILANY</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Prisco</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>7.5416666666667</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">7.54</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>21</span>\n        </samp>]\n        <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">MAMORY</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Andy Stanley</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>5.9583333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.96</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>22</span>\n        </samp>]\n        <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HOLIFARA</span>\"\n          \"<span class=sf-dump-key>prenom</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Andrianina Jaura</span>\"\n          \"<span class=sf-dump-key>moy_raw</span>\" => <span class=sf-dump-num>3.3333333333333</span>\n          \"<span class=sf-dump-key>moy</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.33</span>\"\n          \"<span class=sf-dump-key>mention</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Passable</span>\"\n          \"<span class=sf-dump-key>rang</span>\" => <span class=sf-dump-num>23</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>showResults</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>current_parcours</span>\" => []\n      \"<span class=sf-dump-key>current_niveau</span>\" => []\n      \"<span class=sf-dump-key>current_semestres</span>\" => []\n      \"<span class=sf-dump-key>current_annee</span>\" => []\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Parcour</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>3</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_semestres</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Semestre</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>models</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Niveau</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\AnneeUniversitaire</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">672b8ff3c8d828eded68b8a9e8174f8b964b4e810de3ccc6fa2a416203e3b747</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">msm5g</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">pdfGenerate</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321748746\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-202543613 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3794</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikh3QUxVVWlrYlA2ZkpCTVNTUS9JOHc9PSIsInZhbHVlIjoiVkZOSFFWdEtXSC9weWJIQ2VPUlo4L1FNaTkzZzk0TVF2YVI3SDEvRUdIdXI1dnBpaEh5UUtyTEFtS1NFc2VWQnV4bVhZWDhHb1FqTEVNYUVDNUhSUko1THhNRnhCbkVzSXgxNUQ0REhjMStTRHkzZUlnQ1RzUzdqWm91SVQwWTgiLCJtYWMiOiI5YjdlMTQxMDBhMDBmMjI2NGIzNWU5NzAxNTkxYTA2NWFjNjBhNzllMzMwYmJiZjBiZDI4NjcxNzhkOTFlODI3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InYyMDY4OHZuTk9hZ2JHY25ycHdjaHc9PSIsInZhbHVlIjoiWEQvNjJER1htQWo4WFFEd0hkMm8wNFJkRStYWHBhNzRNMjFUUFdiS3l2TnFpaWpJNUJxVUpuN25uYTh4ejhORjdjQ1dGa0lYaU1XclNVQlVOZForbG5LM00xTkhYU2xwNVJDZ1VXbHlGaFNMcmxzUEQrREFhMGFYdUNvRWdmMTIiLCJtYWMiOiIxZjM5ZGY2ZjEyNmZjOTI1NzRhZTdiNjliY2JjZjcxNmEwYTU4M2JlYTU2MWVjMzA0ZDlmN2U4OGI5MjRhYjUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202543613\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-727323232 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58270</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"34 characters\">/index.php/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3794</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3794</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikh3QUxVVWlrYlA2ZkpCTVNTUS9JOHc9PSIsInZhbHVlIjoiVkZOSFFWdEtXSC9weWJIQ2VPUlo4L1FNaTkzZzk0TVF2YVI3SDEvRUdIdXI1dnBpaEh5UUtyTEFtS1NFc2VWQnV4bVhZWDhHb1FqTEVNYUVDNUhSUko1THhNRnhCbkVzSXgxNUQ0REhjMStTRHkzZUlnQ1RzUzdqWm91SVQwWTgiLCJtYWMiOiI5YjdlMTQxMDBhMDBmMjI2NGIzNWU5NzAxNTkxYTA2NWFjNjBhNzllMzMwYmJiZjBiZDI4NjcxNzhkOTFlODI3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InYyMDY4OHZuTk9hZ2JHY25ycHdjaHc9PSIsInZhbHVlIjoiWEQvNjJER1htQWo4WFFEd0hkMm8wNFJkRStYWHBhNzRNMjFUUFdiS3l2TnFpaWpJNUJxVUpuN25uYTh4ejhORjdjQ1dGa0lYaU1XclNVQlVOZForbG5LM00xTkhYU2xwNVJDZ1VXbHlGaFNMcmxzUEQrREFhMGFYdUNvRWdmMTIiLCJtYWMiOiIxZjM5ZGY2ZjEyNmZjOTI1NzRhZTdiNjliY2JjZjcxNmEwYTU4M2JlYTU2MWVjMzA0ZDlmN2U4OGI5MjRhYjUyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752586807.0759</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752586807</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727323232\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1832126256 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EeIhqoegAUgFKdYeTg2gXSMBO9eZTPzhJ6gMO0UM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832126256\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1064135226 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 13:40:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImhHSHQ5WlNIMWgzK0w0TXF1T1RZOEE9PSIsInZhbHVlIjoiMm5ZMHNNYWdrM1JMenpUZ2xLZC9JaXF2b2R0YjVHWGNyMVRLVkdoYUpMYWp1Q0g1NTVwdjI2T1BEUWpTcHpyZlhNYjZTc0huejZZY3VSN1ZaWE8wcXVubVB0Q05ycVFPRGdiQ2F1cWtld2gwV2x1N1NJRngrMUpQd3lsaS9WVisiLCJtYWMiOiIyODk1YWUxZjA4MDNhMmZlOGMwYjVjMDgzODJlMWVmMjM0MDIzMDRjOWJhZTQyYmJjY2Q0ZDc1MjEzYzE5YmMyIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 15:40:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImN6Ym8zSmJaTTVWLzNQS1RHYm5pemc9PSIsInZhbHVlIjoieDQ5UUExZ2J2NCtKb3NhdEZueElvUDkyMWRFL0IvUitTSkQrdmVpOXZiL3pkRHFxWSswcnYxZkRrM1l4Q0hWa0RwbHJKRlV3cEgxaXFrbzZTOUUxSUg2OXM1T0liME5oUHkvcnFiN0JQbFJEeXFodmROVGVsWFluRHRPV2dCcEkiLCJtYWMiOiIyZDRjNDdlZTVlMTQwZDYxMjQ0ZWFkMGJjM2EzZGRiM2U3OWJjNjUzMjhhMWY5NTI1ZTcwNWM2YzcxNjI0YzhlIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 15:40:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImhHSHQ5WlNIMWgzK0w0TXF1T1RZOEE9PSIsInZhbHVlIjoiMm5ZMHNNYWdrM1JMenpUZ2xLZC9JaXF2b2R0YjVHWGNyMVRLVkdoYUpMYWp1Q0g1NTVwdjI2T1BEUWpTcHpyZlhNYjZTc0huejZZY3VSN1ZaWE8wcXVubVB0Q05ycVFPRGdiQ2F1cWtld2gwV2x1N1NJRngrMUpQd3lsaS9WVisiLCJtYWMiOiIyODk1YWUxZjA4MDNhMmZlOGMwYjVjMDgzODJlMWVmMjM0MDIzMDRjOWJhZTQyYmJjY2Q0ZDc1MjEzYzE5YmMyIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 15:40:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImN6Ym8zSmJaTTVWLzNQS1RHYm5pemc9PSIsInZhbHVlIjoieDQ5UUExZ2J2NCtKb3NhdEZueElvUDkyMWRFL0IvUitTSkQrdmVpOXZiL3pkRHFxWSswcnYxZkRrM1l4Q0hWa0RwbHJKRlV3cEgxaXFrbzZTOUUxSUg2OXM1T0liME5oUHkvcnFiN0JQbFJEeXFodmROVGVsWFluRHRPV2dCcEkiLCJtYWMiOiIyZDRjNDdlZTVlMTQwZDYxMjQ0ZWFkMGJjM2EzZGRiM2U3OWJjNjUzMjhhMWY5NTI1ZTcwNWM2YzcxNjI0YzhlIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 15:40:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064135226\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jm4CK2gk1YpoOp4PgYDFpLf1Ac6ooGz3eiJUrWaG</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752585532</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}